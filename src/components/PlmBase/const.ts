import {
  ElDatePicker,
  ElInput,
  ElInputNumber,
  ElUpload,
  ElSwitch,
  ElSelect,
  ElCheckboxGroup
} from 'element-plus'
import { ElCascader } from '@/components/ElCascader'
import { getDictByCodeApi, queryCascade } from '@/api/common'
import { colorDrop, getSizeAndStandardValue } from '@/components/Business/SelectPlus/src/api'
import ColorInfoDialog from '@/views/basic-library-manage/components/ColorInfoDialog.vue'
// 关于约束的相关映射
export const constraintMap = {
  typeMap: {
    INT: 'INT',
    STRING: 'STRING',
    BOOLEAN: 'BOOLEAN',
    ATTACHMENT: 'ATTACHMENT',
    OBJECT: 'OBJECT',
    RICH_TEXT: 'RICH_TEXT',
    FLOAT: 'FLOAT',
    DATE_TIME: 'DATE_TIME'
  },
  componentTypeMap: {
    STRING: 'INPUT_STRING_TYPE',
    INT: 'INPUT_NUMBER_TYPE',
    BOOLEAN: 'SWITCH_TYPE',
    ATTACHMENT: 'UPLOAD_TYPE',
    OBJECT: 'SELECT_TYPE',
    RICH_TEXT: 'TEXTAREA_TYPE',
    FLOAT: 'INPUT_NUMBER_TYPE',
    DATE_TIME: 'DATE_TYPE'
  },
  componentRap: {
    BOOLEAN: ElSwitch,
    STRING: ElInput,
    OBJECT: ElSelect,
    NUMBER: ElInputNumber,
    DATE: ElDatePicker,
    REGEX: ElInput,
    SELECT: ElSelect,

    INT: ElInputNumber,
    ATTACHMENT: ElUpload,
    RICH_TEXT: ElInput,
    FLOAT: ElInputNumber,
    DATE_TIME: ElDatePicker,
    SELECT_TYPE: ElSelect,
    INPUT_STRING_TYPE: ElInput,
    INPUT_NUMBER_TYPE: ElInputNumber,
    SWITCH_TYPE: ElSwitch,
    UPLOAD_TYPE: ElUpload,
    TEXTAREA_TYPE: ElInput,
    DATE_TYPE: ElDatePicker,

    CASCADER: ElCascader,
    RADIOGROUP: ElRadioGroup,
    CHECKBOXGROUP: ElCheckboxGroup
  }
}
//属性映射
export const keyMap = {
  link: 'link',
  readonly: 'readonly',
  minLength: 'minLength',
  maxLength: 'maxLength',
  rows: 'rows',
  clearable: 'clearable',
  disabled: 'disabled',
  required: 'required',
  filterable: 'filterable',
  placeholder: 'placeholder',
  multiple: 'multiple',
  precision: 'precision',
  editable: 'editable',
  limit: 'limit',
  accept: 'accept',
  sizeLimit: 'sizeLimit',
  format: 'format',
  // 数字类型约束
  min: 'min',
  max: 'max',
  step: 'step',
  // 日期类型约束
  dateType: 'dateType',
  // 上传类型约束
  maxSize: 'maxSize',
  // 字符串验证约束
  pattern: 'pattern',
  dataType: 'dataType',
  isRange: 'isRange',
  options: 'options',
  component: 'component',
  regex: 'regex',
  unit: 'unit',
  caseConfiguration: 'caseConfiguration'
}

//文件类型
export const FileType = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  FILE: 'FILE'
}

//时间映射
export const timeMap = {
  HHMMSS: 'HH:mm:ss',
  YYYYMMDD: 'YYYY-MM-DD',
  YYYYMMDDHHMMSS: 'YYYY-MM-DD HH:mm:ss'
}

//列表字段固定类型
export const tableFixedType = {
  left: 'FIXEDLEFT',
  right: 'FIXEDRIGHT',
  none: 'NOFIXED'
}

// 字段搜索行为
export const tableSearchType = {
  search: 'NEEDSEARCH',
  noSearch: 'NOSEARCH',
  defaultSearch: 'DEFAULTSEARCH'
}
export const searchTypeMap = {
  equal: 'EQUAL'
}
// 针对不同字段类型的搜索类型映射
export const fieldTypeSearchOptions = {
  // 字符串类型
  STRING: [
    { label: '等于', value: 'EQUAL' },
    { label: '不等于', value: 'NOEQUAL' },
    { label: '包含', value: 'CONTAIN' },
    { label: '不包含', value: 'NOCONTAIN' },
    { label: '为空', value: 'EMPTY' },
    { label: '不为空', value: 'NOEMPLTY' }
  ],
  // 数字类型（整数、浮点数）
  INT: [
    { label: '等于', value: 'EQUAL' },
    { label: '不等于', value: 'NOEQUAL' },
    { label: '大于', value: 'GREATERTHAN' },
    { label: '小于', value: 'LESSTHAN' },
    { label: '自至', value: 'FROMTO' },
    { label: '为空', value: 'EMPTY' },
    { label: '不为空', value: 'NOEMPLTY' }
  ],
  FLOAT: [
    { label: '等于', value: 'EQUAL' },
    { label: '不等于', value: 'NOEQUAL' },
    { label: '大于', value: 'GREATERTHAN' },
    { label: '小于', value: 'LESSTHAN' },
    { label: '自至', value: 'FROMTO' },
    { label: '为空', value: 'EMPTY' },
    { label: '不为空', value: 'NOEMPLTY' }
  ],
  // 日期时间类型
  DATE_TIME: [
    { label: '等于', value: 'EQUAL' },
    { label: '不等于', value: 'NOEQUAL' },
    { label: '大于', value: 'GREATERTHAN' },
    { label: '小于', value: 'LESSTHAN' },
    { label: '自至', value: 'FROMTO' },
    { label: '为空', value: 'EMPTY' },
    { label: '不为空', value: 'NOEMPLTY' }
  ],
  // 布尔类型
  BOOLEAN: [
    { label: '等于', value: 'EQUAL' },
    { label: '不等于', value: 'NOEQUAL' }
  ],
  // 对象类型（下拉选择）
  OBJECT: [
    { label: '等于', value: 'EQUAL' },
    { label: '不等于', value: 'NOEQUAL' },
    { label: '包含', value: 'CONTAIN' },
    { label: '不包含', value: 'NOCONTAIN' },
    { label: '为空', value: 'EMPTY' },
    { label: '不为空', value: 'NOEMPLTY' }
  ],
  // 附件类型
  ATTACHMENT: [
    { label: '为空', value: 'EMPTY' },
    { label: '不为空', value: 'NOEMPLTY' }
  ],
  // 富文本类型
  RICH_TEXT: [
    { label: '包含', value: 'CONTAIN' },
    { label: '不包含', value: 'NOCONTAIN' },
    { label: '为空', value: 'EMPTY' },
    { label: '不为空', value: 'NOEMPLTY' }
  ]
}

export const caseConfiguration = {
  upper: 'CAPITAL',
  lower: 'LOWER',
  none: 'NONE'
}
export const sourceType = {
  relationModal: 'relationModal'
}
export const ObjectSource = {
  //枚举
  ENUM: {
    apiConfig: {
      config: { label: 'label', value: 'value' },
      api: getDictByCodeApi
    }
  },
  GOODS_CATEGORY: {
    apiConfig: {
      config: { label: 'categoryCnName', value: 'categoryCode', children: 'childList' },
      api: queryCascade
    }
  },
  //材料
  MATERIAL: {
    apiConfig: {
      config: { label: 'categoryCnName', value: 'id', children: 'childList' },
      api: queryCascade
    }
  },
  // 分类
  CATEGORY: {
    apiConfig: {
      config: { label: 'categoryCnName', value: 'categoryCode', children: 'childList' },
      api: queryCascade
    }
  },
  //配色
  COLORDROP: {
    apiConfig: {
      config: { key: 'id', label: 'englishName', value: 'id' },
      api: colorDrop
    },
    displayType: sourceType.relationModal,
    dialogComponent: ColorInfoDialog
  },

  //尺码段
  SIZEANDVALUE: {
    apiConfig: {
      config: {
        key: 'value',
        label: 'label',
        children: 'children',
        value: 'value',
        disabled: 'disable'
      },
      api: getSizeAndStandardValue
    }
  }
}
//配置需要枚举下拉的code
export const constArr = {
  searchEnum: 'ENUMERATED_225'
}
