<script setup lang="ts">
import { computed, ref, reactive, type PropType } from 'vue'
import { Search } from '@element-plus/icons-vue'
import OssUpload from '@/components/Upload/OssUpload.vue'
import { ApiSelect } from '@/components/ApiSelect'
import {
  ElTooltip,
  ElInput,
  ElInputNumber,
  ElSwitch,
  ElRadio,
  ElCheckbox,
  ElMessage
} from 'element-plus'
import { useRouter } from 'vue-router'
import {
  caseConfiguration,
  constraintMap,
  keyMap,
  ObjectSource,
  sourceType,
  timeMap
} from '@/components/PlmBase/const'

defineOptions({
  name: 'FormItemRender'
})

const { typeMap } = constraintMap

// TypeScript interfaces
interface ConstraintItem {
  value: any
  [key: string]: any
}

interface ConstraintList {
  [key: string]: ConstraintItem
}
type DefaultValueType = string | number | boolean | any[] | Record<string, any>

const props = defineProps({
  constraintType: {
    type: String,
    required: true
  },
  constraintList: {
    type: Object as PropType<ConstraintList>,
    required: true
  },
  modelValue: {
    type: [String, Number, Boolean, Array, Object] as PropType<DefaultValueType>,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: ''
  },
  options: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  // 为了处理表单里面回显的内容拿到value+ItemName
  formData: {
    type: Object,
    default: () => ({})
  },
  field: {
    type: String,
    default: ''
  }
})
const emit = defineEmits<{
  'update:modelValue': [value: DefaultValueType]
  changeDialogName: [value: string]
}>()

// 区间验证函数
const validateRange = (value: DefaultValueType): DefaultValueType => {
  if (!isRange.value || !Array.isArray(value) || value.length !== 2) {
    return value
  }

  const [start, end] = value

  // 如果开始值或结束值为空，直接返回
  if (start == null || end == null) {
    return value
  }

  // 数字类型区间验证
  if (props.constraintType === typeMap.INT || props.constraintType === typeMap.FLOAT) {
    const startNum = Number(start)
    const endNum = Number(end)

    if (!isNaN(startNum) && !isNaN(endNum) && startNum > endNum) {
      ElMessage.warning('开始值不能大于结束值')
      // 交换值或者重置为原来的值
      return [endNum, startNum]
    }
  }

  // 日期时间类型区间验证
  if (props.constraintType === typeMap.DATE_TIME) {
    const startDate = new Date(start as string | number | Date)
    const endDate = new Date(end as string | number | Date)

    if (startDate.getTime() > endDate.getTime()) {
      ElMessage.warning('开始时间不能晚于结束时间')
      // 交换值
      return [end, start]
    }
  }

  return value
}

// 本地值管理
const localValue = computed({
  get: () => {
    const value = props.modelValue

    // 只对字符串类型进行大小写转换
    if (props.constraintType === typeMap.STRING && typeof value === 'string') {
      const caseConfig = props.constraintList?.[keyMap.caseConfiguration]?.value

      if (caseConfig === caseConfiguration.upper) {
        return value.toUpperCase()
      } else if (caseConfiguration.lower) {
        return value.toLowerCase()
      }
    }

    return value
  },
  set: (val: DefaultValueType) => {
    let processedValue = val

    // 只对字符串类型进行大小写转换
    if (props.constraintType === typeMap.STRING && typeof val === 'string') {
      const caseConfig = props.constraintList?.[keyMap.caseConfiguration]?.value

      if (caseConfig === caseConfiguration.upper) {
        processedValue = val.toUpperCase()
      } else if (caseConfig === caseConfiguration.lower) {
        processedValue = val.toLowerCase()
      }
    }

    // 区间验证
    if (isRange.value) {
      processedValue = validateRange(processedValue)
    }

    emit('update:modelValue', processedValue)
  }
})
// 计算API配置
const bindApiConfig = computed(() => {
  if (props.constraintType !== typeMap.OBJECT) {
    return null
  }
  const options = props.constraintList?.[keyMap.options]?.value
  if (!options || !options?.type) {
    return null
  }
  const { apiConfig } = (options.type && ObjectSource?.[options.type]) || {}
  let params = {}
  if (options.type === 'ENUM') {
    params = { dictCode: options.value }
  } else {
    params = {
      type: options.type,
      value: options.value === options.type ? '' : options.value
    }
  }
  return props.options && props.options.length > 0
    ? {
        options: props.options
      }
    : {
        apiConfig,
        params,
        config: apiConfig?.config
      }
})
// 是否为多选
const isMultiple = computed(() => {
  return props.constraintList?.[keyMap.multiple]?.value || false
})

// 是否为区间
const isRange = computed(() => {
  return props.constraintList?.[keyMap.isRange]?.value || false
})

// 获取时间格式
const getTimeFormat = computed(() => {
  const format = timeMap[props.constraintList?.[keyMap.format]?.value]
  return format || 'YYYY-MM-DD HH:mm:ss'
})

// 获取组件属性
const getComponentProps = computed(() => {
  const baseProps: Record<string, any> = {
    disabled: props.disabled,
    placeholder:
      props.constraintList?.[keyMap.placeholder]?.value ||
      props.placeholder ||
      getDefaultPlaceholder(),
    clearable: props.constraintList?.[keyMap.clearable]?.value !== false
  }

  // 根据约束类型添加特定属性
  switch (props.constraintType) {
    case typeMap.STRING:
      return {
        ...baseProps,
        maxlength: props.constraintList?.[keyMap.maxLength]?.value,
        minlength: props.constraintList?.[keyMap.minLength]?.value,
        readonly: props.constraintList?.[keyMap.readonly]?.value
      }

    case typeMap.RICH_TEXT:
      return {
        ...baseProps,
        type: 'textarea',
        rows: props.constraintList?.[keyMap.rows]?.value || 3,
        maxlength: props.constraintList?.[keyMap.maxLength]?.value,
        showWordLimit: !!props.constraintList?.[keyMap.maxLength]?.value
      }

    case typeMap.INT:
    case typeMap.FLOAT:
      return {
        ...baseProps,
        min: props.constraintList?.[keyMap.min]?.value,
        max: props.constraintList?.[keyMap.max]?.value,
        step: props.constraintList?.[keyMap.step]?.value || 1,
        precision:
          props.constraintType === typeMap.FLOAT
            ? props.constraintList?.[keyMap.precision]?.value || 2
            : undefined
      }

    case typeMap.OBJECT:
      return {
        ...baseProps,
        multiple: isMultiple.value,
        props: { multiple: isMultiple.value, emitPath: false },
        filterable: props.constraintList?.[keyMap.filterable]?.value
      }

    case typeMap.DATE_TIME:
      return {
        ...baseProps,
        type: isRange.value ? 'datetimerange' : 'date',
        format: getTimeFormat.value,
        valueFormat: getTimeFormat.value
      }

    case typeMap.ATTACHMENT:
      return {
        ...baseProps,
        limit: props.constraintList?.[keyMap.limit]?.value || 1,
        accept: props.constraintList?.[keyMap.accept]?.value,
        sizeLimit: props.constraintList?.[keyMap.maxSize]?.value || 1024 * 1024 * 50,
        listType:
          props.constraintList?.[keyMap.dataType]?.value === 'IMAGE' ? 'picture-card' : 'text'
      }

    default:
      return baseProps
  }
})

// 获取默认占位符
const getDefaultPlaceholder = () => {
  const placeholderMap: Record<string, string> = {
    [typeMap.STRING]: '请输入',
    [typeMap.INT]: '请输入',
    [typeMap.FLOAT]: '请输入',
    [typeMap.RICH_TEXT]: '请输入',
    [typeMap.DATE_TIME]: '请选择',
    [typeMap.OBJECT]: '请选择'
  }
  return placeholderMap[props.constraintType] || '请输入'
}

// 获取组件类型
const getComponent = computed(() => {
  const component = props.constraintList?.[keyMap.component]?.value
  return constraintMap.componentRap[component] || ''
})

const checkIsObjectDialog = computed(() => {
  if (props.constraintType !== typeMap.OBJECT) {
    return false
  }
  const options = props.constraintList?.[keyMap.options]?.value
  const { displayType, dialogComponent, apiConfig } =
    (options && options.type && ObjectSource[options.type]) || {}
  //如果需要弹窗的形式
  if (displayType === sourceType.relationModal) {
    return {
      dialogComponent,
      apiConfig
    }
  }
  return false
})
const getCheckObjectDialogLabel = computed(() => {
  return relationDialog.localName || props.formData[props.field + 'ItemName'] || localValue.value
})
const relationDialog = reactive({
  visible: false,
  localName: ''
})
const handleRelationDialog = () => {
  relationDialog.visible = true
}
const submitRelation = (row) => {
  const list = [row]
  if (!checkIsObjectDialog.value) {
    return false
  }
  const { apiConfig } = checkIsObjectDialog.value
  const key = apiConfig.config.key
  const label = apiConfig.config.label
  const idSet = new Set(list.map((item) => item[key]))
  localValue.value = Array.from(idSet)
  relationDialog.localName = list.map((item) => item[label])?.join(',')
  emit('changeDialogName', relationDialog.localName)
}

// ApiSelect组件的ref
const apiSelectRef = ref()

// 处理显示文本的截断和悬浮显示
const getDisplayText = (
  value: any,
  maxItems = 2
): { display: string; full: string; needTooltip: boolean } => {
  if (!value) return { display: '', full: '', needTooltip: false }

  // 如果是数组类型（多选）
  if (Array.isArray(value)) {
    if (value.length === 0) return { display: '', full: '', needTooltip: false }

    // 获取选项标签
    const getOptionLabel = (val: any) => {
      if (bindApiConfig.value?.options) {
        const option = bindApiConfig.value.options.find((opt) => opt.value === val)
        return option?.label || val
      }
      if (apiSelectRef.value?.formatOptions) {
        const option = apiSelectRef.value.formatOptions.find((opt) => opt.value === val)
        return option?.label || val
      }
      return val
    }

    const labels = value.map(getOptionLabel)
    const fullText = labels.join(', ')

    if (labels.length <= maxItems) {
      return { display: fullText, full: fullText, needTooltip: false }
    }

    const displayLabels = labels.slice(0, maxItems)
    const remainingCount = labels.length - maxItems
    const displayText = `${displayLabels.join(', ')}... (+${remainingCount})`

    return { display: displayText, full: fullText, needTooltip: true }
  }

  // 如果是单个值
  const stringValue = String(value)
  if (stringValue.length <= 50) {
    // 单行文本长度限制
    return { display: stringValue, full: stringValue, needTooltip: false }
  }

  const displayText = stringValue.substring(0, 47) + '...'
  return { display: displayText, full: stringValue, needTooltip: true }
}
// 获取当前值的显示信息
const displayInfo = computed(() => {
  return getDisplayText(localValue.value)
})

// 检查是否为只读状态
const isReadonly = computed(() => {
  return props.constraintList?.[keyMap.readonly]?.value || false
})

// 检查是否为链接
const isLink = computed(() => {
  if (!isReadonly.value) return false

  const value = props.constraintList?.[keyMap.link]?.value.trim()
  // 检查是否为URL
  const urlPattern = /^(https?:\/\/|www\.)/i
  // 检查是否为路由名称（简单判断：不包含协议且不是纯数字）
  const routePattern = /^[a-zA-Z][a-zA-Z0-9_-]*$/

  return urlPattern.test(value) || routePattern.test(value)
})

// 获取链接类型和目标
const linkInfo = computed(() => {
  if (!isLink.value) return null

  const value = props.constraintList?.[keyMap.link]?.value.trim()
  const urlPattern = /^(https?:\/\/|www\.)/i

  if (urlPattern.test(value)) {
    // 是URL链接
    const url = value.startsWith('www.') ? `https://${value}` : value
    return {
      type: 'url',
      target: url,
      display: value
    }
  } else {
    // 是路由名称
    return {
      type: 'route',
      target: value,
      display: value
    }
  }
})

// 判断是否有更多选项（用于多选下拉框）
const hasMoreItems = computed(() => {
  if (!Array.isArray(localValue.value)) return false
  return localValue.value.length > 2
})

// 获取选择器的动态属性
const getSelectProps = computed(() => {
  const props = { ...getComponentProps.value }
  if (hasMoreItems.value) {
    props['data-has-more'] = 'true'
  }
  return props
})

// 检查区间值是否有效
const isRangeValid = computed(() => {
  if (!isRange.value || !Array.isArray(localValue.value) || localValue.value.length !== 2) {
    return true
  }

  const [start, end] = localValue.value

  // 如果任一值为空，认为是有效的（未完成输入）
  if (start == null || end == null) {
    return true
  }

  // 数字类型验证
  if (props.constraintType === typeMap.INT || props.constraintType === typeMap.FLOAT) {
    const startNum = Number(start)
    const endNum = Number(end)
    return !(!isNaN(startNum) && !isNaN(endNum) && startNum > endNum)
  }

  // 日期类型验证
  if (props.constraintType === typeMap.DATE_TIME) {
    const startDate = new Date(start)
    const endDate = new Date(end)
    return !(startDate.getTime() > endDate.getTime())
  }

  return true
})
// 处理链接点击
const handleLinkClick = () => {
  if (!linkInfo.value) return

  if (linkInfo.value.type === 'url') {
    // 打开URL链接
    window.open(linkInfo.value.target, '_blank')
  } else if (linkInfo.value.type === 'route') {
    // 路由跳转
    try {
      const router = useRouter()
      router.push({ name: linkInfo.value.target })
    } catch (error) {
      console.error('路由跳转失败:', error)
      ElMessage.error(`无法跳转到路由: ${linkInfo.value.target}`)
    }
  }
}

// 处理数字区间变化
const handleNumberRangeChange = (value: number | null, index: 0 | 1) => {
  if (!Array.isArray(localValue.value)) {
    localValue.value = [null, null]
  }
  const newValue = [...(localValue.value as (number | null)[])]
  newValue[index] = value

  // 验证区间 - 只有当两个值都存在时才验证
  if (newValue[0] != null && newValue[1] != null) {
    const [start, end] = newValue
    if (Number(start) > Number(end)) {
      ElMessage.warning('开始值不能大于结束值，已自动调整')
      // 自动调整：如果用户修改的是开始值，则将结束值设为开始值
      // 如果用户修改的是结束值，则将开始值设为结束值
      if (index === 0) {
        newValue[1] = value // 将结束值设为新的开始值
      } else {
        newValue[0] = value // 将开始值设为新的结束值
      }
    }
  }

  localValue.value = newValue
}

// 处理日期区间变化
const handleDateRangeChange = (value: any) => {
  // 如果值为空或不是数组，直接设置
  if (!value || !Array.isArray(value) || value.length !== 2) {
    localValue.value = value
    return
  }

  const [start, end] = value

  // 如果开始时间或结束时间为空，直接设置
  if (!start || !end) {
    localValue.value = value
    return
  }

  const startDate = new Date(start)
  const endDate = new Date(end)

  // 验证日期有效性
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    localValue.value = value
    return
  }

  // 如果开始时间晚于结束时间，交换它们
  if (startDate.getTime() > endDate.getTime()) {
    ElMessage.warning('开始时间不能晚于结束时间，已自动调整')
    localValue.value = [end, start]
    return
  }

  localValue.value = value
}

// 暴露ApiSelect的方法和数据
defineExpose({
  // 如果是ApiSelect组件，暴露其方法
  get formatOptions() {
    return apiSelectRef.value?.formatOptions || []
  },
  get options() {
    return apiSelectRef.value?.options || []
  },
  fetchOptions() {
    return apiSelectRef.value?.fetchOptions?.()
  },
  apiSelectRef
})
</script>

<template>
  <div class="simple-form-item-render">
    <!-- 字符串类型 -->
    <template v-if="constraintType === typeMap.STRING">
      <!-- 只读状态且为链接时显示可点击链接 -->
      <ElTooltip
        v-if="isReadonly && isLink"
        :content="`点击${linkInfo?.type === 'url' ? '打开链接' : '跳转到页面'}: ${
          linkInfo?.display
        }`"
        placement="top"
        :show-after="500"
        popper-class="form-item-tooltip"
      >
        <span class="form-link" @click="handleLinkClick" :title="linkInfo?.display">
          {{ linkInfo?.display }}
        </span>
      </ElTooltip>

      <!-- 普通输入框 -->
      <ElTooltip
        v-else
        :content="displayInfo.full"
        :disabled="!displayInfo.needTooltip"
        placement="top"
        :show-after="500"
        popper-class="form-item-tooltip"
      >
        <ElInput v-model="localValue" v-bind="getComponentProps" class="truncate-input" />
      </ElTooltip>
    </template>

    <!-- 富文本类型 -->
    <ElTooltip
      v-else-if="constraintType === typeMap.RICH_TEXT"
      :content="displayInfo.full"
      :disabled="!displayInfo.needTooltip"
      placement="top"
      :show-after="500"
      popper-class="form-item-tooltip"
    >
      <ElInput v-model="localValue" v-bind="getComponentProps" class="truncate-input" />
    </ElTooltip>

    <!-- 数字类型 -->
    <template v-else-if="constraintType === typeMap.INT || constraintType === typeMap.FLOAT">
      <!-- 区间类型 -->
      <div v-if="isRange" class="number-range" :class="{ 'range-invalid': !isRangeValid }">
        <ElInputNumber
          :model-value="localValue?.[0]"
          @update:model-value="(val) => handleNumberRangeChange(val, 0)"
          v-bind="getComponentProps"
          style="width: 120px"
          placeholder="开始值"
          :class="{ 'is-error': !isRangeValid }"
        />
        <span class="range-separator">~</span>
        <ElInputNumber
          :model-value="localValue?.[1]"
          @update:model-value="(val) => handleNumberRangeChange(val, 1)"
          v-bind="getComponentProps"
          style="width: 120px"
          placeholder="结束值"
          :class="{ 'is-error': !isRangeValid }"
        />
      </div>
      <!-- 单个数字 -->
      <ElInputNumber v-else v-model="localValue" v-bind="getComponentProps" style="width: 100%" />
    </template>
    <!-- 布尔类型 -->
    <ElSwitch
      v-else-if="constraintType === typeMap.BOOLEAN"
      v-model="localValue"
      :disabled="disabled"
    />

    <!-- 日期时间类型 -->
    <ElDatePicker
      v-else-if="constraintType === typeMap.DATE_TIME"
      :model-value="localValue"
      @update:model-value="isRange ? handleDateRangeChange : (val) => (localValue = val)"
      v-bind="getComponentProps"
      style="width: 100%"
      :start-placeholder="isRange ? '开始时间' : undefined"
      :end-placeholder="isRange ? '结束时间' : undefined"
      :class="{ 'is-error': isRange && !isRangeValid }"
    />
    <!-- 对象类型（选择器） -->
    <template v-else-if="constraintType === typeMap.OBJECT">
      <!-- API选择器 -->
      <ElTooltip
        v-if="checkIsObjectDialog"
        :content="getCheckObjectDialogLabel"
        :disabled="!getCheckObjectDialogLabel || getCheckObjectDialogLabel.length <= 50"
        placement="top"
        :show-after="500"
        popper-class="form-item-tooltip"
      >
        <ElInput
          search
          readonly
          :suffix-icon="Search"
          :disabled="disabled"
          v-model="getCheckObjectDialogLabel"
          @click="handleRelationDialog"
          class="truncate-input"
        />
      </ElTooltip>

      <template v-else>
        <!--API选择器--->
        <ElTooltip
          v-if="bindApiConfig && bindApiConfig?.apiConfig"
          :content="displayInfo.full"
          :disabled="!displayInfo.needTooltip"
          placement="top"
          :show-after="500"
          popper-class="form-item-tooltip"
        >
          <ApiSelect
            ref="apiSelectRef"
            v-model="localValue"
            v-bind="{
              ...getSelectProps,
              ...bindApiConfig,
              component: getComponent,
              childComponent:
                getComponent.name === 'ElCheckboxGroup'
                  ? ElCheckbox
                  : getComponent.name === 'ElRadioGroup'
                  ? ElRadio
                  : undefined
            }"
            class="truncate-select api-select"
          />
        </ElTooltip>

        <!--静态选项选择器--->
        <ElTooltip
          v-else-if="bindApiConfig && bindApiConfig?.options"
          :content="displayInfo.full"
          :disabled="!displayInfo.needTooltip"
          placement="top"
          :show-after="500"
          popper-class="form-item-tooltip"
        >
          <component
            :is="getComponent"
            v-model="localValue"
            v-bind="{
              ...bindApiConfig,
              ...getSelectProps
            }"
            class="truncate-select api-select"
          >
            <template v-if="getComponent?.name === 'ElSelect'">
              <ElOption
                v-for="item in bindApiConfig.options"
                :key="item.value"
                :class="{ 'option-disabled': item.disabled }"
                :label="item.label"
                :value="item.value!"
              />
            </template>
          </component>
        </ElTooltip>
      </template>
    </template>
    <!-- 附件类型 -->
    <OssUpload
      v-else-if="constraintType === typeMap.ATTACHMENT"
      v-model="localValue"
      v-bind="getComponentProps"
    />
    <ElInput v-else v-model="localValue" v-bind="getComponentProps" />
    <span class="unit"> {{ constraintList?.[keyMap.unit]?.value }}</span>
    <component
      @submit="submitRelation"
      v-if="constraintType === typeMap.OBJECT && checkIsObjectDialog"
      :is="checkIsObjectDialog.dialogComponent"
      v-model="relationDialog.visible"
    />
  </div>
</template>

<style scoped lang="less">
.option-disabled {
  color: rgba(204, 204, 204, 0.76);
  pointer-events: none;
}

.simple-form-item-render {
  display: flex;
  width: 100%;

  .unit {
    margin-left: 4px;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }

  .number-range {
    display: flex;
    align-items: center;
    gap: 8px;

    .range-separator {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-secondary);
      user-select: none;
    }

    // 区间输入框样式优化
    .el-input-number {
      :deep(.el-input__inner) {
        text-align: center;
      }

      // 错误状态样式
      &.is-error {
        :deep(.el-input__inner) {
          border-color: var(--el-color-danger);
          box-shadow: 0 0 0 1px var(--el-color-danger) inset;
        }
      }
    }

    // 无效区间的整体样式
    &.range-invalid {
      .range-separator {
        font-weight: 600;
        color: var(--el-color-danger);
      }
    }
  }

  // 日期选择器错误状态
  .el-date-editor.is-error {
    :deep(.el-input__wrapper) {
      border-color: var(--el-color-danger);
      box-shadow: 0 0 0 1px var(--el-color-danger) inset;
    }
  }
}

// 截断输入框样式
.truncate-input {
  :deep(.el-input__inner) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :deep(.el-textarea__inner) {
    display: -webkit-box;
    max-height: calc(1.4em * 2); // 2行的高度
    overflow: hidden;
    line-height: 1.4;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-box-orient: vertical;
    line-clamp: 2; // 最多显示2行
  }
}
// 截断选择器样式

.truncate-select {
  :deep(.el-select__wrapper) {
    // 单选模式的样式
    // 多选模式的样式（只有在多选时才应用截断）
    .el-select__selection {
      max-height: 64px;
      min-height: 24px;
      overflow-y: auto;

      .el-select__selected-item {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      // 多选标签样式
      .el-tag {
        max-width: 120px;
        margin: 2px 4px 2px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .el-tag__content {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      // 多选时只显示前2个标签，其余隐藏
      .el-tag:nth-child(n + 3) {
        display: none !important;
      }
    }

    // 添加更多提示（只在多选且有更多项时显示）
    &.is-multiple[data-has-more='true'] .el-select__selection::after {
      position: absolute;
      top: 50%;
      right: 25px;
      margin-left: 4px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      content: '...';
      transform: translateY(-50%);
    }
  }
  // 复选框组样式（只在有很多选项时才截断）
  :deep(.el-checkbox-group) {
    &.has-many-options {
      position: relative;
      max-height: 60px; // 约2行的高度
      overflow: hidden;

      .el-checkbox:nth-child(n + 3) {
        display: none !important;
      }

      &::after {
        position: absolute;
        right: 5px;
        bottom: 5px;
        padding: 0 4px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        background: var(--el-bg-color);
        content: '...';
      }
    }
  }

  // 单选框组样式（只在有很多选项时才截断）
  :deep(.el-radio-group) {
    &.has-many-options {
      position: relative;
      max-height: 60px; // 约2行的高度
      overflow: hidden;

      .el-radio:nth-child(n + 3) {
        display: none !important;
      }

      &::after {
        position: absolute;
        right: 5px;
        bottom: 5px;
        padding: 0 4px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        background: var(--el-bg-color);
        content: '...';
      }
    }
  }
}
// 链接样式
.form-link {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  color: var(--el-color-primary);
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &:hover {
    color: var(--el-color-primary-light-3);
    text-decoration: underline;
  }

  &:active {
    color: var(--el-color-primary-dark-2);
  }
}

// 全局tooltip样式
:deep(.el-tooltip__popper) {
  max-width: 800px;
  word-wrap: break-word;
  white-space: pre-wrap;

  &.is-dark {
    background-color: var(--el-color-info-dark-2);
    border: 1px solid var(--el-border-color);
  }
}

// FormItemRender专用tooltip样式
:global(.form-item-tooltip) {
  width: auto !important;
  max-width: 450px !important;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style>
