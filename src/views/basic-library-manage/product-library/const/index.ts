import { VxeTableDefines } from 'vxe-table'

export interface Column<D = any> extends VxeTableDefines.ColumnOptions<D> {
  editable?: boolean
}

export enum MemberTypeEnums {
  /**
   * 设计师
   */
  DESIGNER = 'DESIGNER',
  /**
   * 开发人员
   */
  DEVELOPER = 'DEVELOPER',
  /**
   * 企划
   */
  PLANNER = 'PRODUCT_PLANING'
}

export enum ImportTypeEnum {
  PRODUCT = 'product',
  ORDER_VOLUME = 'orderVolume',
  PRODUCT_PRICE = 'productPrice',
  QUICK_CREATE = 'quickCreate',
  UPDATE_SKC = 'updateSKC',
  UPDATE_ALL_PRODUCT = 'updateAllProduct'
}

export enum AddCategoryTypeEnum {
  ADD = 1,
  UPDATE = 2
}

// 产品选品会结果
export enum ProductSelectResultEnum {
  /**
   * 选中
   */
  SELECTED = '1',
  /**
   * 未选中
   */
  UNSELECTED = '0'
}

// 产品阶段
export enum ProductStageEnum {
  /**
   * 齐色样阶段
   */
  COLOR = 'UNIFORM_COLOR_SAMPLE_PHASE',

  /**
   * 确认样阶段
   */
  CONFIRMATION = 'CONFIRMATION_SAMPLE_PHASE',

  /**
   * 开款阶段
   */
  PAYMENT = 'DESIGN_PHASE',

  /**
   * 商品准备阶段
   */
  PREPARATION = 'PRODUCT_PREPARATION_PHASE',

  /**
   * 大货生产阶段
   */
  PRODUCTION = 'BULK_PRODUCTION_PHASE',

  /**
   * 初样阶段
   */
  SAMPLE = 'INITIAL_SAMPLE_PHASE',

  /**
   * 选品会阶段
   */
  SELECT_MEETING = 'PRODUCT_SELECTION_MEETING_PHASE'
}

// 产品任务节点
export enum ProductTaskNodeEnum {
  /**
   * 运营信息完善
   */
  PRODUCT_OPERATION_IMPROVEMENT = 'PRODUCT_OPERATION_IMPROVEMENT',
  /**
   * 开款阶段
   */
  OPENING_STAGE = 'ENTRY_REGISTRATION',
  /**
   * 款式设计
   */
  STYLE_DESIGN = 'DESIGN_STYLE',
  /**
   * 打样分配
   */
  PROOFING_DISTRIBUTION = 'SAMPLE_ALLOCATION',
  /**
   * 初样跟进
   */
  FIRST_SAMPLE_FOLLOW = 'INITIAL_SAMPLE_FOLLOWUP',
  /**
   * 初样评审
   */
  FIRST_SAMPLE_REVIEW = 'INITIAL_SAMPLE_REVIEW',
  /**
   * 工厂报价
   */
  FACTORY_QUOTATION = 'FACTORY_QUOTATION',
  /**
   * 齐色配置
   */
  COLOR_CONFIGURATION = 'COLOR_CONFIGURATION',
  /**
   * 齐色样跟进
   */
  COLOR_SAMPLE_FOLLOW = 'COLOR_SAMPLE_FOLLOWUP',
  /**
   * 齐色样评审
   */
  COLOR_SAMPLE_REVIEW = 'COLOR_SAMPLE_REVIEW',
  /**
   * 选品结论
   */
  PRODUCT_CONCLUSION = 'SELECTION_CONCLUSION',
  /**
   * 侵权排查
   */
  INFRINGEMENT_INVESTIGATION = 'RIGHTS_INFRINGEMENT_CHECK',
  /**
   * 材质信息
   */
  MATERIAL_INFORMATION = 'MATERIAL_INFORMATION',
  /**
   * 材质占比
   */
  MATERIAL_PERCENT_INFORMATION = 'MATERIAL_PERCENT_INFORMATION',
  /**
   * 包装维护
   */
  PACKAGING_MAINTENANCE = 'PACKAGING_MAINTENANCE',
  /**
   * 商品建档
   */
  PRODUCT_ARCHIVING = 'PRODUCT_DOCUMENTATION',
  /**
   * 成本报价
   */
  COST_QUOTATION = 'COST_QUOTATION',
  /**
   * 植绒初判
   */
  FLOCKING_JUDGMENT = 'FLOCKING_INITIAL_JUDGMENT',
  /**
   * 确认样配置
   */
  CONFIRM_SAMPLE_OUT = 'CONFIRMATION_SAMPLE_CONFIGURATION',
  /**
   * 确认样跟进
   */
  CONFIRM_SAMPLE_FOLLOW = 'CONFIRMATION_SAMPLE_FOLLOW_UP',
  /**
   * 确认样评审
   */
  CONFIRM_SAMPLE_REVIEW = 'CONFIRMATION_SAMPLE_REVIEW',
  /**
   * 植绒更新
   */
  FLOCKING_UPDATE = 'FLOCKING_UPDATE',
  /**
   * 包装更新
   */
  PACKAGING_UPDATES = 'PACKAGING_UPDATE'
}

// 楦底类型
export enum LastBottomTypeEnum {
  /**
   * 新底新楦
   */
  NEW_BOTTOM_NEW_LAST = 'nhnl',
  /**
   * 新底老楦
   */
  NEW_BOTTOM_OLD_LAST = 'nhol',
  /**
   * 老底新楦
   */
  OLD_BOTTOM_NEW_LAST = 'ohnl',
  /**
   * 老底老楦
   */
  OLD_BOTTOM_OLD_LAST = 'ohol',
  /**
   * 直接采购
   */
  DIRECT_PURCHASE = 'buyin'
}

/**
 * 基本计量单位
 */
export enum BaseUnitEnum {
  /**
   * 双
   */
  PAIR = 'pair',
  /**
   * 只
   */
  SINGLE = 'single'
}

/**
 * 开发模式
 */
export enum DevelopModeEnum {
  /**
   * 正季
   */
  FULL_SEASON = 'fullSeason',
  /**
   * 滚动
   */
  SCROLL = 'scroll',
  /**
   * 快反
   */
  QUICK_REACTION = 'quickReaction'
}

/**
 *防水级别
 */
export enum PFLevelEnum {
  /**
   * 不防水
   */
  NONE = 'Not Water Resistant'
}

/**
 * 材料维护类型
 */
export enum MaterialTypeEnum {
  /**
   * 已选中
   */
  SELECTED = 'selected'
}

/**
 * 产品衍生类型
 */
export enum ProductDerivativeTypeEnum {
  /**
   * 原始款
   */
  ORIGIN_VERSION = 'ORIGIN_VERSION',
  /**
   * 情侣亲子
   */
  COUPLE_PARENT_CHILDREN = 'COUPLE_PARENT_CHILDREN',
  /**
   * 楦型调整
   */
  LAST_SHAPE_ADJUST = 'LAST_SHAPE_ADJUST',
  /**
   * 同款多配
   */
  MULTIPLE_ACCESSORIES = 'MULTIPLE_ACCESSORIES',
  /**
   * VOC改款
   */
  VOC_RESTYLE = 'VOC_RESTYLE',
  /**
   * 款式改善
   */
  VOC_IMPROVE = 'VOC_IMPROVE'
}
//相关对象相关
export const enum RelatedObjectMapping {
  relatedProduct = 'relatedProduct',
  relatedMolds = 'relatedMolds',
  relatedSellingPoints = 'relatedSellingPoints',
  relatedLastTypes = 'relatedLasts', //相关楦型数据
  relatedProductStyles = 'relatedModels' //相关形体
}
