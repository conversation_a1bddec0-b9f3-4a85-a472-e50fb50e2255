<script lang="ts" setup>
import { ref } from 'vue'
import { FormModel } from '@/views/basic-library-manage/product-library/api/productInfo'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { sizeList as getSizeList } from '@/components/Business/SelectPlus/src/api'
import {
  BrandEnum,
  HeadCategoryEnum,
  HeadStandardEnum,
  shoesToeCategoryList,
  StatusEnum,
  TargetGroupEnum
} from '@/views/basic-library-manage/const'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { SizeValueAPI } from '@/views/basic-library-manage/size-library/api/sizeInfo'
import { ProductDerivativeTypeEnum } from '@/views/basic-library-manage/product-library/const'
import {
  DictValueAPI,
  getProductCategoryList,
  ProductCategoryListAPI
} from '@/views/basic-library-manage/api/common'
import { productDerive } from '@/views/basic-library-manage/product-library/api/deriveDialog'
import { useAllDictStore } from '@/store/modules/dictAll'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import { SizeCodeTypeEnums } from '@/enums'

defineOptions({
  name: 'DeriveDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow?: FormModel
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

type DeriveFormData = FormModel & { vocDerivedType?: string }
const formRef = ref<FormInstance>()

const formData = ref<DeriveFormData>({})
type FormProps = Omit<DeriveFormData, 'skcInfoDetailResp'>
const formRules = reactive<FormRules<FormProps>>({
  brand: [{ required: true, message: '请选择品牌', trigger: 'change' }],
  launchSeason: [{ required: true, message: '请选择开发季节', trigger: 'change' }],
  targetAudience: [{ required: true, message: '请选择适用人群', trigger: 'change' }],
  sizeRangeId: [{ required: true, message: '请选择尺码段', trigger: 'change' }],
  standardSizeId: [{ required: true, message: '请选择标准码', trigger: 'change' }],
  derivedType: [{ required: true, message: '请选择衍生类型', trigger: 'change' }],
  lastsStandard: [{ required: true, message: '请选择楦型标准', trigger: 'change' }],
  toeStandard: [{ required: true, message: '请选择楦头标准', trigger: 'change' }],
  multipleAccessoriesType: [{ required: true, message: '请选择同款多配类型', trigger: 'change' }],
  productCategory: [{ required: true, message: '请选择产品类目', trigger: 'change' }],
  vocDerivedType: [{ required: true, message: '请选择款式改善类型', trigger: 'change' }]
})
watch(visible, (val) => {
  if (val && props.currentRow) {
    let index = 0
    const derivedTypeList = currentDerivedTypeList.value
    const item = derivedTypeList[index]
    while (index < derivedTypeList.length && getDerivedTypeDisabled(item)) {
      index += 1
    }
    if (index < derivedTypeList.length) {
      formData.value.derivedType = derivedTypeList[index].value
    }
    fetchProductCategoryList()
  }
})

const handleClose = () => {
  visible.value = false
  formData.value = {}
}

type ItemName = `${keyof FormModel}ItemName`

type DescriptionsItem = {
  label: string
  field: keyof FormModel | ItemName
  span?: number
}

const descriptionsItem: DescriptionsItem[] = [
  { field: 'productNumber', label: '产品编号' },
  { field: 'thumbnail', label: '缩略图' },
  { field: 'brandItemName', label: '品牌' },
  { field: 'styleWms', label: 'Style（WMS）' },
  { field: 'launchSeason', label: '开发季节' },
  { field: 'productCategoryItemName', label: '产品类目' },
  { field: 'targetAudienceItemName', label: '适用人群' },
  { field: 'productStyleItemName', label: '产品风格' },
  { field: 'applicableSeasonItemName', label: '适用季节' },
  { field: 'styleStructureItemName', label: '款式结构' },
  { field: 'lastsStandardItemName', label: '楦型标准' },
  { field: 'toeStandardItemName', label: '楦头类别' },
  { field: 'derivedType', label: '产品衍生类型' },
  { field: 'styleNumber', label: 'Style编号' }
]

const basicLibraryDictStore = useBasicLibraryDictStore()
const allDictStore = useAllDictStore()
const deriveTypeList = computed(() => basicLibraryDictStore.derivedTypeList)

const multiMatchedOptions = computed(() => basicLibraryDictStore.multiMatchedList)

const brandList = computed(() => basicLibraryDictStore.brandList)
const filterBrandList = computed(() => {
  const dp: number[] = [BrandEnum.DP, BrandEnum.DPK]
  if (props.currentRow?.brand && dp.includes(props.currentRow?.brand)) {
    return brandList.value.filter(
      (e) => typeof e.dictValue === 'number' && dp.includes(e.dictValue)
    )
  }
  return [
    {
      dictCnName: props.currentRow?.brandItemName,
      dictEnName: props.currentRow?.brandItemName,
      dictValue: props.currentRow?.brand
    }
  ]
})

const targetAudienceList = computed(() => basicLibraryDictStore.productPeopleList)
const filterTargetAudienceList = computed<DictValueAPI.Data[]>(() => {
  const targetAudience = props.currentRow?.targetAudience
  if (targetAudience === TargetGroupEnum.KIDS) {
    return targetAudienceList.value.filter(
      (e) => e.dictValue === TargetGroupEnum.WOMEN || e.dictValue === TargetGroupEnum.MEN
    )
  }
  if (targetAudience === TargetGroupEnum.WOMEN) {
    return targetAudienceList.value.filter(
      (e) => e.dictValue === TargetGroupEnum.MEN || e.dictValue === TargetGroupEnum.KIDS
    )
  }
  if (targetAudience === TargetGroupEnum.MEN) {
    return targetAudienceList.value.filter(
      (e) => e.dictValue === TargetGroupEnum.WOMEN || e.dictValue === TargetGroupEnum.KIDS
    )
  }
  return []
})

const lastTypeList = computed(() => basicLibraryDictStore.lastTypeList)
const productLastStandardList = computed(() => basicLibraryDictStore.productLastStandardList)
const filterLastList = computed(() => {
  const { lastsStandard } = props.currentRow || {}
  if (!lastsStandard) {
    return {
      lastsStandard: [],
      toeStandard: []
    }
  }
  const toeRules = {
    [HeadStandardEnum.U]: shoesToeCategoryList, // 美楦: 宽楦、窄楦、正常楦
    [HeadStandardEnum.E]: shoesToeCategoryList, // 欧楦: 宽楦、窄楦、正常楦
    [HeadStandardEnum.D]: shoesToeCategoryList, // 欧楦专供: 宽楦、窄楦、正常楦
    [HeadStandardEnum.A]: [HeadCategoryEnum.A]
  }

  const lastRules = {
    [HeadStandardEnum.U]: [HeadStandardEnum.U, HeadStandardEnum.E],
    [HeadStandardEnum.E]: [HeadStandardEnum.U, HeadStandardEnum.E],
    [HeadStandardEnum.D]: [HeadStandardEnum.D],
    [HeadStandardEnum.A]: [HeadStandardEnum.A]
  }

  // 过滤楦型标准
  const filteredLastsStandard = productLastStandardList.value.filter((last) =>
    lastRules[lastsStandard].includes(last.dictValue!)
  )

  // 根据楦型标准过滤楦头类别
  const filteredToeStandard = lastTypeList.value.filter((toe) =>
    toeRules[lastsStandard].includes(toe.dictValue)
  )

  return {
    lastsStandard: filteredLastsStandard,
    toeStandard: filteredToeStandard
  }
})

const currentDerivedTypeList = computed<{ label: string; value: string }[]>(() => {
  const string = deriveTypeList.value.find(
    (e) => e.dictCnName === props.currentRow?.derivedType
  )?.dictValue
  if (string && typeof string === 'string') {
    const list = JSON.parse(string)
    return list.map((item) => {
      const [value, label] = Object.entries(item)[0]
      return { label, value: value.trim() }
    })
  }
  return []
})

const sizeList = ref<(SizeListAPI.Data & { disabled?: boolean })[]>([])
const filteredSizeList = computed(() => {
  let lastsStandard = formData.value.lastsStandard
  let toeStandard = formData.value.toeStandard
  if (!lastsStandard && !toeStandard) {
    lastsStandard = props.currentRow?.lastsStandard
    toeStandard = props.currentRow?.toeStandard
  }

  return sizeList.value
    .map((e) => ({
      ...e,
      disabled:
        e.disabled ||
        (lastsStandard === HeadStandardEnum.U && e.lastsStandard !== HeadStandardEnum.U) ||
        (lastsStandard === HeadStandardEnum.E && e.lastsStandard !== HeadStandardEnum.E) ||
        (toeStandard === HeadCategoryEnum.W && e.toeStandard !== HeadCategoryEnum.W)
    }))
    .sort((a, b) => +a.disabled! - +b.disabled!)
})
const getAllSizeList = async () => {
  const { datas } = await getSizeList()
  if (datas) {
    sizeList.value = datas
      .filter((e) => e.sizeType === SizeCodeTypeEnums.PRODUCT)
      .map((e) => ({
        ...e,
        disabled: e.status !== StatusEnum.START
      }))
      .sort((a, b) => +a.disabled! - +b.disabled!)
  }
}

type SizeValueList = {
  name?: string
  standard?: SizeValueAPI.Row
  sizeValueList: (SizeValueAPI.Row & { disabled: boolean })[]
}

const sizeValueList = ref<SizeValueList[]>([])

const standardSizeValueList = computed(() => {
  return sizeValueList.value
    .map((e) =>
      e.sizeValueList.map((size) => ({
        label: `${size.sizeValue} 【${e.name}】`,
        disabled: size.disabled,
        value: size.id
      }))
    )
    .flat()
})

const handleSizeRangeIdChange = () => {
  if (!formData.value.sizeRangeId?.length) {
    sizeValueList.value = []
    formData.value.standardSizeId = []
    return
  }
  sizeValueList.value = filteredSizeList.value
    .filter((e) => formData.value.sizeRangeId?.includes(e.id!))
    .map((e) => ({
      name: e.name,
      standard: e.sizeValueList?.find((e) => e.standard),
      sizeValueList: (e.sizeValueList || []).map((e) => ({
        ...e,
        disabled: e.status !== StatusEnum.START
      }))
    }))
  formData.value.standardSizeId = sizeValueList.value
    .filter((e) => e.standard)
    .map((e) => e.standard!.id!)
}

Promise.all([getAllSizeList()])

const handleDerivedTypeChange = (derivedType: string) => {
  sizeValueList.value = []
  formData.value = {
    derivedType: derivedType
  }
  if (ProductDerivativeTypeEnum.MULTIPLE_ACCESSORIES === derivedType) {
    formData.value.productCategory = props.currentRow?.productCategory
  }
}

const handleClearSize = () => {
  formData.value.sizeRangeId = []
  handleSizeRangeIdChange()
}

const getDerivedTypeDisabled = (item: (typeof currentDerivedTypeList.value)[number]) => {
  if (props.currentRow?.targetAudience === TargetGroupEnum.UNION) {
    return item.value === ProductDerivativeTypeEnum.COUPLE_PARENT_CHILDREN
  }
  if (props.currentRow?.lastsStandard === HeadStandardEnum.A) {
    return item.value === ProductDerivativeTypeEnum.LAST_SHAPE_ADJUST
  }
  return false
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) {
    return
  }
  submitLoading.value = true
  const submitData = {
    ...props.currentRow,
    ...formData.value,
    id: undefined
  }
  // voc改款(款式改善) 后台的传值字段为derivedType
  if (formData.value.derivedType === ProductDerivativeTypeEnum.VOC_IMPROVE) {
    submitData.derivedType = formData.value.vocDerivedType
  }

  const res = await productDerive(submitData)
  submitLoading.value = false
  if (res) {
    emit('refresh')
    ElMessage.success(res.msg || '创建成功')
    handleClose()
  }
}

// 重置
watch(visible, () => {
  formData.value = {}
})

// 款式改善options
const vocImproveOptions = computed(() => {
  return allDictStore.dictToOptions('VOC_IMPROVE')
})

const productCategoryList = ref<ProductCategoryListAPI.Data[]>([])

const fetchProductCategoryList = async () => {
  const [error, result] = await getProductCategoryList()
  if (error === null && result?.datas) {
    //衍生前的产品类目所属大类
    productCategoryList.value = (result?.datas || []).map((item: ProductCategoryListAPI.Data) => {
      return {
        disabled: item.selectorCode !== props.currentRow?.productCategoryItemCode,
        ...item
      }
    })
  }
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    title="产品衍生"
    top="5vh"
    width="1200"
  >
    <ElDivider content-position="left">产品信息【当前】</ElDivider>
    <Descriptions :data="props.currentRow" :schema="descriptionsItem">
      <template #thumbnail="{ row }: { row: FormModel }">
        <ElImage
          :preview-src-list="[row.thumbnail?.signatureUrl!]"
          :src="row.thumbnail?.signatureUrl"
          class="!h-[80px] !w-[80px] shadow inline-block"
          loading="lazy"
          hide-on-click-modal
          style="min-width: 80px !important"
        />
      </template>
    </Descriptions>
    <ElDivider content-position="left">产品信息【衍生】</ElDivider>
    <ElForm
      v-if="currentDerivedTypeList.length"
      ref="formRef"
      v-loading="submitLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
    >
      <ElFormItem label="衍生类型" prop="derivedType">
        <ElRadioGroup v-model="formData.derivedType" @change="handleDerivedTypeChange">
          <ElRadio
            v-for="item in currentDerivedTypeList"
            :key="item.value"
            :disabled="getDerivedTypeDisabled(item)"
            :label="item.label"
            :value="item.value"
          />
        </ElRadioGroup>
      </ElFormItem>
      <div class="grid grid-cols-2 items-center">
        <!-- 衍生类型=情侣亲子 -->
        <template v-if="formData.derivedType === ProductDerivativeTypeEnum.COUPLE_PARENT_CHILDREN">
          <ElFormItem label="品牌" prop="brand">
            <ElSelect v-model="formData.brand" clearable filterable placeholder="请选择品牌">
              <ElOption
                v-for="item in filterBrandList"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="开发季节" prop="launchSeason">
            <SelectPlus
              v-model="formData.launchSeason"
              api-key="COMMON_MARKET_SEASON"
              cache
              clearable
              filterable
              placeholder="请选择开发季节"
            />
          </ElFormItem>
          <ElFormItem label="适用人群" prop="targetAudience">
            <ElSelect
              v-model="formData.targetAudience"
              clearable
              filterable
              placeholder="请选择适用人群"
            >
              <ElOption
                v-for="item in filterTargetAudienceList"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="尺码段" prop="sizeRangeId">
            <ElSelect
              v-model="formData.sizeRangeId"
              clearable
              filterable
              multiple
              placeholder="请选择尺码段"
              @change="handleSizeRangeIdChange"
            >
              <ElOption
                v-for="item in filteredSizeList"
                :key="item.id"
                :disabled="item.disabled"
                :label="item.name"
                :value="item.id!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="标准码" prop="standardSizeId">
            <ElSelect v-model="formData.standardSizeId" disabled filterable multiple>
              <ElOption
                v-for="item in standardSizeValueList"
                :key="item.value"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="尺码值">
            <span v-for="item in sizeValueList" :key="item.name">
              {{
                item.sizeValueList
                  .filter((e) => !e.disabled)
                  .map((e) => e.sizeValue)
                  .join(', ')
              }}
              <span>【{{ item.name }}】</span>
            </span>
          </ElFormItem>
          <div class="col-span-2 w-full flex justify-end mb-8">
            <div class="p-4 text-red-500 mr-8">
              注意：
              <br />
              1、产品衍生时，开发季节与衍生前保持一致，则为衍生款；不一致则衍生后为全新款。
              <br />
              2、衍生后的产品：商品上市季节与页面选择的开发季节保持一致。
            </div>
          </div>
        </template>
        <!-- 衍生类型=同款多配 -->
        <template
          v-else-if="formData.derivedType === ProductDerivativeTypeEnum.MULTIPLE_ACCESSORIES"
        >
          <ElFormItem label="同款多配类型" prop="multipleAccessoriesType">
            <ElSelect
              v-model="formData.multipleAccessoriesType"
              filterable
              placeholder="请选择同款多配类型"
            >
              <ElOption
                v-for="item in multiMatchedOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="产品类目" prop="productCategory">
            <ElCascader
              ref="productCategoryRef"
              v-model="formData.productCategory"
              :options="productCategoryList"
              :props="{
                emitPath: false,
                value: 'selectorKey',
                label: 'selectorEnValue',
                children: 'childList'
              }"
              clearable
              filterable
              placeholder="请选择"
            />
          </ElFormItem>
          <ElFormItem label="开发季节" prop="launchSeason">
            <SelectPlus
              v-model="formData.launchSeason"
              api-key="COMMON_MARKET_SEASON"
              cache
              clearable
              filterable
              placeholder="请选择开发季节"
            />
          </ElFormItem>
          <div class="col-span-2 w-full flex justify-end mb-8">
            <div class="p-4 text-red-500 mr-8">
              注意：
              <br />
              1、产品衍生时，产品类目与衍生前保持一致，则为衍生款；不一致则衍生后为全新款。
              <br />
              2、产品衍生时，开发季节变化不影响产品衍生类型。
              <br />
              2、衍生后的产品：商品上市季节与页面选择的开发季节保持一致。
            </div>
          </div>
        </template>
        <!-- 衍生类型=楦型调整 -->
        <template v-else-if="formData.derivedType === ProductDerivativeTypeEnum.LAST_SHAPE_ADJUST">
          <ElFormItem label="楦型标准" prop="lastsStandard">
            <ElSelect
              v-model="formData.lastsStandard"
              clearable
              filterable
              placeholder="请选择楦型标准"
              @change="handleClearSize"
            >
              <ElOption
                v-for="item in filterLastList.lastsStandard"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="楦头类别" prop="toeStandard">
            <ElSelect
              v-model="formData.toeStandard"
              clearable
              filterable
              placeholder="请选择楦头类别"
              @change="handleClearSize"
            >
              <ElOption
                v-for="item in filterLastList.toeStandard"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="尺码段" prop="sizeRangeId">
            <ElSelect
              v-model="formData.sizeRangeId"
              clearable
              filterable
              multiple
              placeholder="请选择尺码段"
              @change="handleSizeRangeIdChange"
            >
              <ElOption
                v-for="item in filteredSizeList"
                :key="item.id"
                :disabled="item.disabled"
                :label="item.name"
                :value="item.id!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="标准码" prop="standardSizeId">
            <ElSelect v-model="formData.standardSizeId" disabled filterable multiple>
              <ElOption
                v-for="item in standardSizeValueList"
                :key="item.value"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="尺码值">
            <span v-for="item in sizeValueList" :key="item.name">
              {{
                item.sizeValueList
                  .filter((e) => !e.disabled)
                  .map((e) => e.sizeValue)
                  .join(', ')
              }}
              <span>【{{ item.name }}】</span>
            </span>
          </ElFormItem>
          <ElFormItem label="开发季节" prop="launchSeason">
            <SelectPlus
              v-model="formData.launchSeason"
              api-key="COMMON_MARKET_SEASON"
              cache
              clearable
              filterable
              placeholder="请选择开发季节"
            />
          </ElFormItem>
          <div class="col-span-2 w-full flex justify-end mb-8">
            <div class="p-4 text-red-500 mr-8">
              注意：
              <br />
              1、产品衍生时，开发季节变化不影响产品衍生类型。
              <br />
              2、衍生后的产品：商品上市季节与页面选择的开发季节保持一致。
            </div>
          </div>
        </template>
        <!-- 衍生类型=款式改善 -->
        <template v-else-if="formData.derivedType === ProductDerivativeTypeEnum.VOC_IMPROVE">
          <ElFormItem label="款式改善类型" prop="vocDerivedType">
            <ElSelect v-model="formData.vocDerivedType">
              <ElOption
                v-for="item in vocImproveOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="开发季节" prop="launchSeason">
            <SelectPlus
              v-model="formData.launchSeason"
              api-key="COMMON_MARKET_SEASON"
              cache
              clearable
              filterable
              placeholder="请选择开发季节"
            />
          </ElFormItem>
          <div class="col-span-2 w-full flex justify-end mb-8">
            <div class="p-4 text-red-500 mr-8">
              注意：
              <br />
              1、产品衍生时，开发季节变化不影响产品衍生类型。
              <br />
              2、衍生后的产品：商品上市季节与页面选择的开发季节保持一致。
            </div>
          </div>
        </template>
      </div>
    </ElForm>
    <ElEmpty v-else description="当前产品没有可衍生类型" />
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
