<script setup lang="ts">
import { useHook } from '@/views/NewBasicLibraryManage/components/Help/useHook'
import ProductMessage from '@/views/NewBasicLibraryManage/Product/components/ProductMessage.vue'
import { AttributeData, layoutAttribute } from '@/components/PlmBase/type'
import { needAttribute, YNEnum } from '@/views/NewBasicLibraryManage/components/Help/Detail'
import {
  convertProducts,
  getCopySkcDetail,
  getMultiProductBaseDetail,
  getProductSkcDetails,
  productCopy
} from '@/api/NewProductLibraryManage/detail'
import { queryFormDataValue } from '@/components/PlmBase/help'
import ProductColor from '@/views/NewBasicLibraryManage/Product/components/ProductColor.vue'
import { hasPermission } from '@/directives/permission/hasPermi'
import { getHeadDetail } from '@/api/NewProductLibraryManage/detail'
const route = useRoute()
const router = useRouter()
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const routeMeta = JSON.parse(otherMeta)

const typeId = computed(() => routeMeta.productId)
const skcId = computed(() => routeMeta.skcId)
const type = computed(() => routeMeta.type)
const pageLayoutCode = computed(() => routeMeta.pageLayoutCode)

const {
  AllAttribute,
  pageLoading,
  getLayout,
  formData,
  filterAttributeTableList,
  isDataLoaded,
  waitForData
} = useHook(
  {
    needProductAttribute: true,
    needSkcAttribute: true,
    needSkuAttribute: false
  },
  routeMeta
)
const pageLayout = ref<layoutAttribute[]>([])
const headDetail = ref<AttributeData>()
const isCreate = computed(() => {
  return route.name === 'NewAddProduct'
})
const isCopy = computed(() => {
  return route.name === 'NewCopyProduct'
})
//产品配色
const newColor = computed(() => {
  return route.name === 'ColorNewProduct'
})
//详情
const isDetail = computed(() => {
  return route.name === 'NewProductDetail'
})
// 优化后的初始化方法
const initializeData = async () => {
  try {
    // 并发执行布局获取和数据初始化
    const layoutPromise = getLayout(pageLayoutCode.value, typeId.value)

    if (!isCreate.value) {
      // 等待Product属性数据加载完成
      if (!isDataLoaded(typeId.value)) {
        await waitForData(typeId.value, 5000)
      }

      // 并发执行数据获取
      const [layout] = await Promise.all([layoutPromise, getProductMessage(), getHeadDetailFunc()])

      pageLayout.value = layout || []

      // 延迟加载SKC数据，避免阻塞主要内容
      initializeSkcData()
    } else {
      pageLayout.value = (await layoutPromise) || []
    }
  } catch (error) {
    console.error('初始化ProductInfo失败:', error)
  }
}

// 初始化SKC数据
const initializeSkcData = async () => {
  try {
    // 等待SKC属性数据加载完成
    if (!isDataLoaded(skcId.value)) {
      await waitForData(skcId.value, 5000)
    }

    // 加载配色数据
    if (!isCreate.value) {
      await getColor()
    }
  } catch (error) {
    console.error('初始化SKC数据失败:', error)
  }
}

onMounted(() => {
  initializeData()
})
const addMaterailAttriburteList = computed(() => {
  return filterAttributeTableList(needAttribute, typeId.value)
})

// 优化后的获取详细信息方法
const getProductMessage = async () => {
  try {
    let error, result
    if (isCopy.value) {
      //是复制过来的
      ;[error, result] = await productCopy({
        id: route.query.id,
        typeId: typeId.value,
        type: type.value,
        pageLayoutCode: pageLayoutCode.value
      })
    } else {
      ;[error, result] = await getMultiProductBaseDetail({
        id: route.query.id,
        typeId: typeId.value,
        type: type.value,
        pageLayoutCode: pageLayoutCode.value
      })
    }
    if (!error && result.data) {
      formData[typeId.value] = {
        ...formData[typeId.value],
        ...result.data
      }
    }
    // 确保属性数据已加载
    if (isDataLoaded(typeId.value)) {
      queryFormDataValue(AllAttribute[typeId.value], formData[typeId.value])
    } else {
      await waitForData(typeId.value, 3000)
      queryFormDataValue(AllAttribute[typeId.value], formData[typeId.value])
    }
  } catch (error) {
    console.error('获取产品详细信息失败:', error)
  }
}
// 优化后的获取配色信息方法
const getColor = async () => {
  try {
    // 获取SKC数据'
    let result1
    if (isCopy.value) {
      result1 = await getCopySkcDetail(route.query.code as string)
    } else {
      result1 = await getProductSkcDetails({
        productCode: route.query.code as string
      })
    }
    formData[typeId.value].skcInfoDetailResp = result1.data

    // 确保SKC属性数据已加载
    if (isDataLoaded(skcId.value)) {
      formData[typeId.value].skcInfoDetailResp?.forEach((item: AttributeData) => {
        queryFormDataValue(AllAttribute[skcId.value], item)
      })
    } else {
      await waitForData(skcId.value, 3000)
      formData[typeId.value].skcInfoDetailResp?.forEach((item: AttributeData) => {
        queryFormDataValue(AllAttribute[skcId.value], item)
      })
    }
  } catch (error) {
    console.error('获取配色信息失败:', error)
  }
}

const transformFunc = async () => {
  const [error] = await convertProducts([formData[typeId.value].id])
  if (!error) {
    ElMessage.success('转化成功')
    getProductMessage()
    getColor()
    getHeadDetailFunc()
  }
}
//获取头部信息
const getHeadDetailFunc = async () => {
  if (!route.query.id) {
    return
  }
  const id = route.query.id as string
  const result = await getHeadDetail(id)
  if (result?.data) {
    headDetail.value = result.data
  }
}
</script>

<template>
  <ElCard v-if="!isCreate" body-class="!py-2" style="z-index: 9" class="sticky top-0 mb-2">
    <ElScrollbar>
      <div class="flex flex-nowrap items-center w-full min-h-28 h-28 whitespace-nowrap">
        <ElImage
          hide-on-click-modal
          :preview-src-list="[headDetail?.productDesignUrl]"
          :src="headDetail?.productDesignUrl"
          class="w-24 h-24 min-w-24 min-h-24 shadow-md shadow-gray-500/50"
          fit="contain"
          preview-teleported
        />
        <div class="h-1/2 ml-4 mr-2 flex flex-col justify-between">
          <div class="text-center">{{ headDetail?.code }}</div>
          <div>
            <ElTag class="mr-2" effect="dark" round size="large">{{ headDetail?.year }}</ElTag>
            <ElTag class="mr-2" round size="large">
              {{ headDetail?.userName }}
            </ElTag>
          </div>
        </div>
        <ElDivider
          class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
          direction="vertical"
        />
        <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
          <div>供应商</div>
          <div>
            {{ headDetail?.assignedFactory }}
          </div>
        </div>
        <ElDivider
          class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-md shadow-divider"
          direction="vertical"
        />
        <div
          class="flex-1 flex-grow-[2] flex-shrink-[2] h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500"
        >
          <div>产品颜色</div>
          <div class="flex flex-nowrap">
            <div v-for="item in headDetail?.colorImgUrl" :key="item" class="mr-2 w-6 h-6">
              <ElImage
                :preview-src-list="headDetail?.colorImgUrl"
                :src="item"
                class="w-6 h-6"
                fit="contain"
                loading="lazy"
                hide-on-click-modal
                preview-teleported
              />
            </div>
          </div>
        </div>
        <ElDivider
          class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-md shadow-divider"
          direction="vertical"
        />
        <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
          <div>当前状态</div>
          <div>{{ headDetail?.dataStatus }}</div>
        </div>
        <div class="flex flex-col ml-2">
          <ElButton size="large" @click="router.push({ name: 'NewProductList' })">
            返回产品清单
          </ElButton>
          <div class="h-1"></div>
          <ElButton
            v-if="
              formData[typeId]?.dataFlag !== YNEnum.Y &&
              hasPermission(['transform', 'edit-transform'])
            "
            class="!ml-0"
            size="large"
            @click="transformFunc"
          >
            转化为商品
          </ElButton>
        </div>
      </div>
    </ElScrollbar>
  </ElCard>
  <ContentWrap>
    <div v-loading="pageLoading">
      <product-message
        v-if="!newColor"
        :page-layout="pageLayout"
        :allAttribute="AllAttribute"
        :add-material-attribute="addMaterailAttriburteList"
        :formData="formData[typeId]"
      />
      <!--产品配色信息--->
      <product-color
        :add-material-attribute="addMaterailAttriburteList"
        v-else
        :allAttribute="AllAttribute"
        :formData="formData[typeId]?.skcInfoDetailResp"
      />
    </div>
  </ContentWrap>
</template>

<style scoped lang="less"></style>
