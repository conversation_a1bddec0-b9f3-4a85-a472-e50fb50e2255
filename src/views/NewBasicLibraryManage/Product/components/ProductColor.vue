<script setup lang="ts">
import AddMaterial from '@/views/NewBasicLibraryManage/Product/components/AddMaterial.vue'
import { Attribute, AttributeData } from '@/components/PlmBase/type'
import { FormatSizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import {
  getSizeAndStandardValueByCode,
  productUpdate,
  updateStyleColor
} from '@/api/NewProductLibraryManage/detail'
import { computed, ref } from 'vue'
import { VxeTableDefines } from 'vxe-table'
import { ElMessage } from 'element-plus'
import { saveFormDataValue } from '@/components/PlmBase/help'
import { useRouter } from 'vue-router'
defineOptions({
  name: 'NewColor'
})
const props = defineProps<{
  addMaterialAttribute: Attribute[]
  formData: Attribute[]
  allAttribute: Record<string, Attribute[]>
}>()
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const route = useRoute()
const skcData = ref<AttributeData[]>([])
const tableRef = ref<InstanceType<typeof AddMaterial>>()
const skcId = computed(() => JSON.parse(otherMeta).skcId)
const typeId = computed(() => JSON.parse(otherMeta).productId)
const skcType = computed(() => JSON.parse(otherMeta).skcType)
const skcPageLayoutCode = computed(() => JSON.parse(otherMeta).skcPageLayoutCode)

watch(
  () => props.formData,
  () => {
    skcData.value = props.formData
  }
)
const sizeOptions = ref<FormatSizeListAPI.Data[]>([])
const getSizeColor = async () => {
  if (route.query.code) {
    const result = await getSizeAndStandardValueByCode({
      code: route.query.code as string
    })
    const sizeValueList = (result.data || []).flatMap((e) => e.sizeValueList || [])
    sizeOptions.value = sizeValueList.map((e) => ({
      label: `${e.sizeValue}【${e.label}】`,
      value: e.value,
      disabled: e.disabled
    }))
  }
}
const handleClose = () => {
  useClosePage('NewProductList')
}
const saving = ref(false)
// 保存提交
const submit = async () => {
  if (saving.value) return
  // 2. 校验表格数据
  const errorMsg: VxeTableDefines.ValidatorErrorMapParams | undefined | boolean =
    await tableRef.value?.validate?.(true).catch(() => false)
  if (errorMsg) {
    ElMessage.error('产品配色信息校验失败，请检查表格数据')
    return false
  }
  // 3. 创建保存数据的副本，避免影响原始数据
  const saveData = {
    skcReq: JSON.parse(JSON.stringify(tableRef.value?.localFormData || []))
  }
  saveData.skcReq.forEach((item: any) => {
    saveFormDataValue(props.allAttribute[skcId.value], item)
  })
  const params = {
    attributes: saveData.skcReq,
    typeId: typeId.value,
    type: skcType.value,
    pageLayoutCode: skcPageLayoutCode.value
  }
  saving.value = true
  const [error] = await updateStyleColor(params)
  if (!error) {
    ElMessage.success('保存成功')
    handleClose()
  }
  saving.value = false
}
onMounted(() => {
  getSizeColor()
})
</script>

<template>
  <div>
    <AddMaterial
      :addMaterialAttribute="addMaterialAttribute"
      ref="tableRef"
      :sizeOptions="sizeOptions"
      :modelValue="skcData"
    />
  </div>
  <div class="mt-4 text-center">
    <ElButton @click="handleClose">返回</ElButton>
    <ElButton type="primary" :loading="saving" @click="submit"> 确定 </ElButton>
  </div>
</template>

<style scoped lang="less"></style>
