export const VersionAttribute = [
  {
    type: 'checkbox',
    width: 40
  },
  {
    type: 'seq',
    title: '序号',
    width: 60
  },
  {
    field: 'versionCode',
    title: '版本号'
  },
  {
    field: 'versionCode',
    title: '版本变更类型'
  },
  {
    field: 'modifyByName',
    title: '修改者'
  },
  {
    field: 'modifyTime',
    title: '修改时间'
  },
  {
    field: 'versionRemark',
    title: '版本说明'
  },
  {
    title: '操作',
    slots: {
      default: 'operation'
    }
  }
]
export const skuMessageConfig = [
  { field: 'code', title: 'SKU编码' },
  { field: 'colorItemName', title: '产品配色' },
  { field: 'wmsColorName', title: 'WMS配色名称' },
  { field: 'colorAbbreviation', title: '颜色缩略图' },
  { field: 'mainFabricItemName', title: '主要面料' },
  { field: 'selectedSizeItemName', title: '选中尺码' },
  { field: 'outerBoxLong', title: '外箱长(cm)' },
  { field: 'outerBoxWidth', title: '外箱宽(cm)' },
  { field: 'outerBoxHeight', title: '外箱高(cm)' },
  { field: 'outerBoxRoughWeight', title: '外箱毛重(kg)' },
  { field: 'boxNumber', title: '箱规' },
  { field: 'boxVolume', title: '外箱体积(m³)' },
  { field: 'innerBoxWidth', title: '鞋盒宽(cm)' },
  { field: 'innerBoxHeight', title: '鞋盒高(cm)' },
  { field: 'innerBoxLong', title: '鞋盒长(cm)' },
  { field: 'innerBoxRoughWeight', title: '鞋盒毛重(kg)' },
  { field: 'dataStatusItemName', title: '状态' }
]
export const MaterialConfig = [
  {
    title: '产品配色',
    field: 'color'
  },
  {
    title: '主要面料',
    field: 'mainFabric'
  },
  {
    title: '选中尺码',
    field: 'selectedSize'
  },
  {
    title: '样品图',
    field: 'sampleImages'
  }
]
export const needAttribute = [
  {
    title: '产品配色',
    field: 'color'
  },
  {
    title: '主要面料',
    field: 'mainFabric'
  },
  {
    title: '选中尺码',
    field: 'selectedSize'
  }
]
export const needAttributeRule = [
  'skcCode',
  'dataStatusItemName',
  'thumbnail',
  'mainFabric',
  'sampleUrl'
]
export enum YNEnum {
  /**
   * 是
   */
  Y = 'Y',
  /**
   * 否
   */
  N = 'N'
}
