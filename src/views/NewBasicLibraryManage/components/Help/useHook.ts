import { getPage, getPageAttributeList } from '@/api/systemConfiguration/type'
import { FormatFormDataValue } from '@/components/PlmBase/help'
import { Attribute, tableColumn } from '@/components/PlmBase/type'
import { constraintMap } from '@/components/PlmBase/const'
import { ref, reactive, computed, onMounted } from 'vue'

// 全局缓存，避免重复请求
const globalCache = {
  attributes: {} as Record<string, Attribute[]>,
  formData: {} as Record<string, any>,
  loadingPromises: {} as Record<string, Promise<any>>,
  isLoaded: {} as Record<string, boolean>
}
interface UseHookOptions {
  needProductAttribute: boolean
  needSkcAttribute: boolean
  needSkuAttribute: boolean
}
// 优化后的useHook
export const useHook = (
  options: UseHookOptions,
  externalRoute?: {
    productId: string
    skcId: string
    skuId: string
  }
) => {
  const { needProductAttribute, needSkcAttribute, needSkuAttribute } = options
  const pageLoading = ref<boolean>(false)
  const AllAttribute = reactive<Record<string, Attribute[]>>({})
  const formData = reactive({})

  // 获取路由信息
  const currentRoute = useRouter()?.currentRoute
  const otherMeta =
    externalRoute ||
    (currentRoute?.value.meta?.otherMeta
      ? JSON.parse(currentRoute.value.meta.otherMeta as string)
      : {})

  const typeId = computed(() => otherMeta.productId)
  const skcId = computed(() => otherMeta.skcId)
  const skuId = computed(() => otherMeta.skuId)

  // 优化的获取属性方法 - 支持全局缓存和Promise缓存
  const getAttributeList = async (typeId: string): Promise<Attribute[]> => {
    if (!typeId) {
      return []
    }

    // 如果已经加载过，直接返回缓存数据
    if (globalCache.attributes[typeId]) {
      AllAttribute[typeId] = globalCache.attributes[typeId]
      formData[typeId] = globalCache.formData[typeId]
      return globalCache.attributes[typeId]
    }

    // 如果正在加载中，返回现有的Promise
    if (globalCache.loadingPromises[typeId]) {
      const result = await globalCache.loadingPromises[typeId]
      AllAttribute[typeId] = result
      formData[typeId] = globalCache.formData[typeId]
      return result
    }

    // 创建新的加载Promise
    globalCache.loadingPromises[typeId] = (async () => {
      try {
        const [error, result] = await getPageAttributeList({ typeCategoryId: typeId })
        if (error === null && result?.data) {
          // 缓存到全局
          globalCache.attributes[typeId] = result.data
          globalCache.isLoaded[typeId] = true

          // 格式化表单数据
          formatFormData(typeId)

          // 设置到当前实例
          AllAttribute[typeId] = result.data

          return result.data
        }
        return []
      } catch (error) {
        console.error(`获取属性列表失败 (typeId: ${typeId}):`, error)
        return []
      } finally {
        // 清除Promise缓存
        delete globalCache.loadingPromises[typeId]
      }
    })()

    return await globalCache.loadingPromises[typeId]
  }
  // 优化的格式化数据方法 - 支持全局缓存
  const formatFormData = (typeId: string) => {
    // 如果已经格式化过，直接使用缓存
    if (globalCache.formData[typeId]) {
      formData[typeId] = globalCache.formData[typeId]
      return
    }

    const attributeData = ref({})
    const attributes = globalCache.attributes[typeId] || AllAttribute[typeId] || []

    attributes.forEach((item: Attribute) => {
      const constraintValue = JSON.parse(item.constraintValue || '{}')
      const data: Record<string, any> = {} // 约束的条件
      attributeData.value[item.value!] = item.constraintDefaultValue || ''

      Object.keys(constraintValue).forEach((key: string) => {
        data[key] = constraintValue[key]?.value
      })

      if (item.type) {
        FormatFormDataValue(constraintMap.typeMap?.[item.type], attributeData, data, item.value!)
      }
    })

    // 缓存格式化后的数据
    globalCache.formData[typeId] = attributeData.value
    formData[typeId] = attributeData.value
  }
  //获取指定的布局
  const getLayout = (pageLayoutCode: string, typeId: string) => {
    if (!pageLayoutCode) {
      return false
    }
    return new Promise(async (resolve) => {
      const [error, result] = await getPage({
        pageLayoutCode,
        pageTypeCategoryId: typeId
      })
      if (error === null && result?.data) {
        resolve((result.data?.pageAttributes && JSON.parse(result.data?.pageAttributes)) || [])
      }
      resolve([])
    })
  }
  // 优化的初始化方法 - 支持并发加载
  const initializeData = async () => {
    pageLoading.value = true

    try {
      // 并发加载所有需要的属性数据
      const loadPromises: Promise<any>[] = []

      if (needProductAttribute && typeId.value) {
        loadPromises.push(getAttributeList(typeId.value))
      }
      if (needSkcAttribute && skcId.value) {
        loadPromises.push(getAttributeList(skcId.value))
      }
      if (needSkuAttribute && skuId.value) {
        loadPromises.push(getAttributeList(skuId.value))
      }

      // 等待所有数据加载完成
      await Promise.all(loadPromises)
    } catch (error) {
      console.error('初始化数据失败:', error)
    } finally {
      pageLoading.value = false
    }
  }

  onMounted(() => {
    initializeData()
  })

  // 筛选出需要的属性
  const filterAttributeTableList = (attributeTable: tableColumn[], type: string) => {
    if (!AllAttribute[type]) {
      return []
    }
    return attributeTable.map((item) => {
      const attribute = AllAttribute[type].find((attribute) => attribute.value === item.field)
      return {
        ...item,
        ...attribute,
        constraintList: JSON.parse(attribute?.constraintValue || '{}')
      }
    })
  }

  // 检查数据是否已加载
  const isDataLoaded = (typeId: string) => {
    return globalCache.isLoaded[typeId] || false
  }

  // 等待数据加载完成
  const waitForData = async (typeId: string, timeout = 5000): Promise<Attribute[]> => {
    if (globalCache.attributes[typeId]) {
      return globalCache.attributes[typeId]
    }

    return new Promise((resolve, reject) => {
      const startTime = Date.now()

      const checkData = () => {
        if (globalCache.attributes[typeId]) {
          resolve(globalCache.attributes[typeId])
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`等待数据超时: ${typeId}`))
        } else {
          setTimeout(checkData, 100)
        }
      }

      checkData()
    })
  }

  // 强制刷新数据
  const refreshData = async (typeId: string) => {
    // 清除缓存
    delete globalCache.attributes[typeId]
    delete globalCache.formData[typeId]
    delete globalCache.isLoaded[typeId]

    // 重新加载
    return await getAttributeList(typeId)
  }

  return {
    getLayout,
    pageLoading,
    AllAttribute,
    formData,
    filterAttributeTableList,
    // 新增的实用方法
    getAttributeList,
    isDataLoaded,
    waitForData,
    refreshData,
    initializeData
  }
}
